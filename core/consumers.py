import json
import random
import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Room
from events.models import EventStep
from games.models import Game, PictionaryGame, PlayerScore
from games.words import WORD_LIST

logger = logging.getLogger(__name__)


# ============================================================================
# 面向对象的环节处理器架构
# ============================================================================

class BaseEventHandler(ABC):
    """
    所有环节处理器的基础抽象类。
    定义了环节处理的统一接口和通用功能。
    """

    def __init__(self, room_code: str, consumer: 'RoomConsumer'):
        self.room_code = room_code
        self.consumer = consumer
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    @abstractmethod
    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        启动环节的抽象方法。

        Args:
            room: 房间对象
            step: 环节步骤对象

        Returns:
            Tuple[payload_data, error_message]: 成功时返回(数据, None)，失败时返回(None, 错误信息)
        """
        pass

    @abstractmethod
    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """
        处理聊天消息的抽象方法。

        Args:
            user: 发送消息的用户
            payload: 消息载荷

        Returns:
            bool: True表示消息已被处理，False表示应继续常规处理
        """
        pass

    @abstractmethod
    async def handle_timeout(self) -> None:
        """
        处理环节超时的抽象方法。
        """
        pass

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        处理重启请求的默认实现。子类可以重写此方法。

        Args:
            user: 请求重启的用户
            payload: 重启载荷

        Returns:
            Tuple[payload_data, error_message]: 成功时返回(数据, None)，失败时返回(None, 错误信息)
        """
        # 默认实现忽略参数
        _ = user, payload
        return None, "此环节不支持重启功能"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """
        处理自定义动作的默认实现。子类可以重写此方法。

        Args:
            action: 动作名称
            user: 执行动作的用户
            payload: 动作载荷

        Returns:
            bool: True表示动作已被处理，False表示未知动作
        """
        # 默认实现忽略参数
        _ = action, user, payload
        return False

    async def cleanup(self) -> None:
        """
        清理资源的默认实现。子类可以重写此方法。
        """
        pass

    # 通用辅助方法
    async def get_room_with_template(self) -> Optional[Room]:
        """获取带有模板的房间对象"""
        return await get_room_with_template(self.room_code)

    async def broadcast_to_room(self, message_type: str, payload: Dict[str, Any]) -> None:
        """向房间广播消息"""
        await self.consumer.channel_layer.group_send(
            self.consumer.room_group_name,
            {'type': message_type, 'payload': payload}
        )

    async def send_error_to_user(self, message: str) -> None:
        """向用户发送错误消息"""
        await self.consumer.send_error(message)


class PictionaryEventHandler(BaseEventHandler):
    """你画我猜环节处理器"""

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """启动你画我猜游戏"""
        try:
            participants = list(room.participants.all())
            if len(participants) < 1:
                return None, "至少需要1名玩家才能开始游戏。"

            drawer = random.choice(participants)
            word = random.choice(WORD_LIST)

            # 更新房间状态
            room.status = Room.STATUS_IN_PROGRESS
            await database_sync_to_async(room.save)()

            # 创建游戏会话
            game_session, _ = await database_sync_to_async(
                Game.objects.update_or_create
            )(room=room, defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True})

            # 创建你画我猜游戏数据
            await database_sync_to_async(
                PictionaryGame.objects.update_or_create
            )(game=game_session, defaults={'current_word': word, 'current_drawer': drawer})

            return {
                "drawer": drawer.username,
                "word": word,
                "duration": step.duration,
                "room_status": room.status,
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None

        except Exception as e:
            self.logger.error(f"Error starting pictionary game: {e}")
            return None, "启动游戏时发生错误"

    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """处理你画我猜游戏中的消息"""
        try:
            message = payload.get('message', '').strip()
            if not message:
                return False

            # 获取当前游戏状态
            game = await get_pictionary_game(self.room_code)
            if not game:
                return False  # 不是你画我猜游戏，继续常规处理

            # 验证游戏状态
            if not game.current_word or not game.current_drawer:
                await self.send_error_to_user("游戏状态异常，请重新开始。")
                return True

            # 检查是否是绘画者（绘画者不能猜词）
            if user.id == game.current_drawer.id:
                return True  # 静默忽略绘画者的消息

            # 检查是否猜对了
            if message.lower() == game.current_word.lower():
                await self._handle_correct_guess(user, game)
                return True

            return False  # 没猜对，继续常规聊天处理

        except Exception as e:
            self.logger.error(f"Error handling pictionary message: {e}")
            return False

    async def _handle_correct_guess(self, winner, game):
        """处理正确猜词"""
        try:
            # 更新分数
            updated_scores = await update_scores(
                winner=winner,
                drawer=game.current_drawer,
                room=game.game.room
            )

            # 结束回合
            new_room_status = await end_pictionary_round(self.room_code)

            if new_room_status:
                await self.broadcast_to_room('broadcast_round_over', {
                    'winner': winner.username,
                    'word': game.current_word,
                    'room_status': new_room_status,
                    'scores': updated_scores,
                })

        except Exception as e:
            self.logger.error(f"Error handling correct guess: {e}")

    async def handle_timeout(self) -> None:
        """处理你画我猜超时"""
        try:
            game = await get_pictionary_game(self.room_code)
            if game:
                new_room_status = await end_pictionary_round(self.room_code)
                if new_room_status:
                    await self.broadcast_to_room('broadcast_round_over', {
                        'winner': None,  # 超时无获胜者
                        'word': game.current_word,
                        'room_status': new_room_status,
                        'scores': {},  # 超时不更新分数
                        'timeout': True,
                    })
        except Exception as e:
            self.logger.error(f"Error handling pictionary timeout: {e}")

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """处理你画我猜重启"""
        try:
            room = await self.get_room_with_template()
            if not room:
                return None, '房间不存在。'

            # 获取当前步骤
            current_step = await database_sync_to_async(
                lambda: room.event_template.steps.filter(order=room.current_step_order).first()
            )()

            if not current_step:
                return None, '无法找到当前环节信息。'

            # 重新启动游戏
            return await self.start_step(room, current_step)

        except Exception as e:
            self.logger.error(f"Error restarting pictionary: {e}")
            return None, "重新开始游戏时发生错误。"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """处理你画我猜的自定义动作"""
        if action == 'send_drawing':
            return await self._handle_drawing_data(user, payload)
        return False

    async def _handle_drawing_data(self, user, payload: Dict[str, Any]) -> bool:
        """处理绘图数据"""
        try:
            path_data = payload.get('path_data')
            if not path_data:
                return True

            # 验证用户是否有绘画权限
            game = await get_pictionary_game(self.room_code)
            if game and user.id != game.current_drawer.id:
                await self.send_error_to_user("只有绘画者可以绘画。")
                return True

            # 广播绘图数据
            await self.broadcast_to_room('broadcast_drawing_data', {
                'path_data': path_data
            })
            return True

        except Exception as e:
            self.logger.error(f"Error handling drawing data: {e}")
            return True


class FreeChatEventHandler(BaseEventHandler):
    """自由聊天环节处理器"""

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """启动自由聊天环节"""
        try:
            # 更新房间状态
            room.status = Room.STATUS_IN_PROGRESS
            await database_sync_to_async(room.save)()

            return {
                "room_status": room.status,
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None

        except Exception as e:
            self.logger.error(f"Error starting free chat: {e}")
            return None, "启动自由聊天时发生错误"

    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """自由聊天不需要特殊的消息处理，返回False让常规处理继续"""
        _ = user, payload  # 忽略未使用的参数
        return False

    async def handle_timeout(self) -> None:
        """处理自由聊天超时"""
        try:
            room = await self.get_room_with_template()
            if room:
                room.status = Room.STATUS_WAITING
                await database_sync_to_async(room.save)()
                await self.broadcast_to_room('broadcast_step_timeout', {
                    'room_status': room.status
                })
        except Exception as e:
            self.logger.error(f"Error handling free chat timeout: {e}")


class EventHandlerFactory:
    """环节处理器工厂类"""

    _handlers = {
        EventStep.STEP_GAME_PICTIONARY: PictionaryEventHandler,
        EventStep.STEP_FREE_CHAT: FreeChatEventHandler,
    }

    @classmethod
    def create_handler(cls, step_type: str, room_code: str, consumer: 'RoomConsumer') -> Optional[BaseEventHandler]:
        """
        根据环节类型创建对应的处理器

        Args:
            step_type: 环节类型
            room_code: 房间代码
            consumer: WebSocket消费者实例

        Returns:
            BaseEventHandler: 对应的处理器实例，如果类型不支持则返回None
        """
        handler_class = cls._handlers.get(step_type)
        if handler_class:
            return handler_class(room_code, consumer)
        return None

    @classmethod
    def get_supported_types(cls) -> list:
        """获取支持的环节类型列表"""
        return list(cls._handlers.keys())


# ============================================================================
# 原有的数据库辅助函数（保持不变）
# ============================================================================

# All @database_sync_to_async helper functions remain the same as the last complete version.
@database_sync_to_async
def update_scores(winner, drawer, room):
    winner_score, _ = PlayerScore.objects.get_or_create(room=room, player=winner)
    winner_score.score += 10; winner_score.save()
    drawer_score, _ = PlayerScore.objects.get_or_create(room=room, player=drawer)
    drawer_score.score += 5; drawer_score.save()
    scores = PlayerScore.objects.filter(room=room).order_by('-score')
    return {score.player.username: score.score for score in scores}

@database_sync_to_async
def get_pictionary_game(room_code):
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None

@database_sync_to_async
def end_pictionary_round(room_code):
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

@database_sync_to_async
def get_room_with_template(room_code):
    """Get room with its event template and participants."""
    try:
        return Room.objects.select_related('event_template').prefetch_related('participants').get(room_code=room_code)
    except Room.DoesNotExist:
        return None

def advance_to_next_step_sync(room):
    """Advance room to the next step in its event template. (Sync version)"""
    if not room.event_template:
        return None

    # Get the next step based on current_step_order
    next_step = room.event_template.steps.filter(order__gt=room.current_step_order).order_by('order').first()

    if next_step:
        # Update room's current step order
        room.current_step_order = next_step.order
        room.save()
        return next_step

    return None  # No more steps

# Async wrapper
advance_to_next_step = database_sync_to_async(advance_to_next_step_sync)

@database_sync_to_async
def save_room(room):
    """Save room instance to database."""
    room.save()

# 注意：start_pictionary_for_step 函数已移动到 PictionaryEventHandler 类中


class RoomConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer_task = None  # Store the timer task for cancellation
        self.current_handler: Optional[BaseEventHandler] = None  # 当前环节处理器

    async def connect(self):
        if not self.scope['user'] or not self.scope['user'].is_authenticated:
            logger.warning(f"Unauthenticated connection attempt")
            await self.close()
            return

        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'

        try:
            await self.channel_layer.group_add(self.room_group_name, self.channel_name)
            await self.accept()
            logger.info(f"User {self.scope['user'].username} connected to room {self.room_code}")
        except Exception as e:
            logger.error(f"Error connecting user to room {self.room_code}: {e}")
            await self.close()

    async def disconnect(self, close_code):
        _ = close_code  # 忽略未使用的参数
        # Cancel any running timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Cleanup current handler
        if self.current_handler:
            try:
                await self.current_handler.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up handler: {e}")

        if hasattr(self, 'room_group_name'):
            try:
                await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
                logger.info(f"User {self.scope['user'].username} disconnected from room {self.room_code}")
            except Exception as e:
                logger.error(f"Error disconnecting user from room {self.room_code}: {e}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            action = data.get('action')
            payload = data.get('payload', {})
            user = self.scope['user']

            logger.debug(f"Received action '{action}' from user {user.username} in room {self.room_code}")

            if action == 'next_step':
                await self.handle_next_step()
            elif action == 'send_message':
                await self.handle_chat_message(payload)
            elif action == 'restart_game':
                await self.handle_restart_game(payload)
            else:
                # 尝试让当前处理器处理自定义动作
                if self.current_handler:
                    handled = await self.current_handler.handle_custom_action(action, user, payload)
                    if handled:
                        return

                logger.warning(f"Unknown action '{action}' from user {user.username}")
                await self.send_error(f"Unknown action: {action}")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received from user {self.scope['user'].username}: {e}")
            await self.send_error("Invalid message format")
        except Exception as e:
            logger.error(f"Error processing message from user {self.scope['user'].username}: {e}")
            await self.send_error("Internal server error")
    async def handle_next_step(self):
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting next step in room {self.room_code}")

            room = await get_room_with_template(self.room_code)

            if not room:
                logger.error(f"Room {self.room_code} not found")
                await self.send_error('房间不存在。')
                return

            # Get host information safely
            room_host = await database_sync_to_async(lambda: room.host)()
            logger.info(f"Room found: {room.room_code}, host: {room_host}, current_step_order: {room.current_step_order}")

            if room_host != user:
                logger.warning(f"User {user.username} tried to start next step but is not host of room {self.room_code}")
                await self.send_error('只有房主才能开始下一环节。')
                return

            logger.info(f"User {user.username} is confirmed as host, advancing to next step")
            next_step = await advance_to_next_step(room)

            if not next_step:
                logger.info(f"All steps completed for room {self.room_code}")
                await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_event_over'})
                return

            logger.info(f"Starting step {next_step.order} ({next_step.step_type}) in room {self.room_code}")

            # 清理之前的处理器
            if self.current_handler:
                await self.current_handler.cleanup()

            # 创建新的环节处理器
            self.current_handler = EventHandlerFactory.create_handler(
                next_step.step_type, self.room_code, self
            )

            if not self.current_handler:
                logger.error(f"No handler found for step type {next_step.step_type} in room {self.room_code}")
                await self.send_error(f"不支持的环节类型: {next_step.step_type}")
                return

            # 启动环节
            game_data, error = await self.current_handler.start_step(room, next_step)
            if error:
                logger.error(f"Failed to start step {next_step.step_type} in room {self.room_code}: {error}")
                await self.send_error(error)
                return

            logger.info(f"Step {next_step.step_type} started successfully for room {self.room_code}")
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_step_start',
                'payload': game_data
            })

            # 启动计时器
            await self.start_step_timer(next_step.duration)

        except Exception as e:
            logger.error(f"Error handling next_step in room {self.room_code}: {e}", exc_info=True)
            await self.send_error("处理下一环节时发生错误。")
    async def handle_chat_message(self, payload):
        try:
            message = payload.get('message', '').strip()
            user = self.scope['user']

            if not message:
                logger.warning(f"Empty message from user {user.username} in room {self.room_code}")
                return

            # 让当前处理器先处理消息
            if self.current_handler:
                handled = await self.current_handler.handle_message(user, payload)
                if handled:
                    return  # 消息已被处理器处理

            # 常规聊天消息处理
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_chat_message',
                'payload': {'message': message, 'sender': user.username}
            })

        except Exception as e:
            logger.error(f"Error handling chat message from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("发送消息时发生错误。")
    async def handle_restart_game(self, payload):
        """Handle request to restart a game."""
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting restart in room {self.room_code}")

            room = await get_room_with_template(self.room_code)
            if not room:
                await self.send_error('房间不存在。')
                return

            # Check if user is host
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user:
                await self.send_error('只有房主才能重新开始游戏。')
                return

            # 让当前处理器处理重启
            if self.current_handler:
                game_data, error = await self.current_handler.handle_restart(user, payload)
                if error:
                    await self.send_error(error)
                    return

                if game_data:
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_start',
                        'payload': game_data
                    })

                    # 重新启动计时器
                    current_step = await database_sync_to_async(
                        lambda: room.event_template.steps.filter(order=room.current_step_order).first()
                    )()
                    if current_step:
                        await self.start_step_timer(current_step.duration)
            else:
                await self.send_error("当前没有活动的环节可以重启。")

        except Exception as e:
            logger.error(f"Error handling restart game from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("重新开始游戏时发生错误。")

    async def start_step_timer(self, duration_seconds):
        """Start a timer for the current step."""
        # Cancel any existing timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Start new timer
        self.timer_task = asyncio.create_task(self._step_timer(duration_seconds))

    async def _step_timer(self, duration_seconds):
        """Timer coroutine that handles step timeout."""
        try:
            await asyncio.sleep(duration_seconds)
            # Time's up - end the current step
            await self.handle_step_timeout()
        except asyncio.CancelledError:
            logger.info(f"Timer cancelled for room {self.room_code}")
            raise
        except Exception as e:
            logger.error(f"Error in step timer for room {self.room_code}: {e}")

    async def handle_step_timeout(self):
        """Handle when a step times out."""
        try:
            logger.info(f"Step timeout for room {self.room_code}")

            # 让当前处理器处理超时
            if self.current_handler:
                await self.current_handler.handle_timeout()
            else:
                # 如果没有处理器，执行默认的超时处理
                room = await get_room_with_template(self.room_code)
                if room:
                    room.status = Room.STATUS_WAITING
                    await save_room(room)
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_timeout',
                        'payload': {'room_status': room.status}
                    })

        except Exception as e:
            logger.error(f"Error handling step timeout for room {self.room_code}: {e}")

    # --- NEW: Generic error handler ---
    async def send_error(self, message):
        """Sends an error message back to the originating client."""
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- BROADCAST HANDLERS ---
    async def broadcast_step_start(self, event):
        # --- FIX: Create a copy of the payload for each user ---
        payload = event['payload'].copy()
        step_type = payload.get('step_info', {}).get('step_type')
        if step_type == EventStep.STEP_GAME_PICTIONARY:
            if self.scope['user'].username != payload['drawer']:
                # Hide the word for non-drawers by replacing each character with underscore
                word = payload['word']
                payload['word'] = " ".join(["_" for _ in word if _ != ' '])
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event): await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))
    async def broadcast_drawing_data(self, event): await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))
    async def broadcast_round_over(self, event): await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))
    async def broadcast_step_timeout(self, event): await self.send(text_data=json.dumps({'type': 'step_timeout', 'payload': event['payload']}))
    async def broadcast_event_over(self, _event): await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))
